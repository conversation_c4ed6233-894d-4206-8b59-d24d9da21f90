import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/tweets_provider.dart';
import '../../widgets/tweet_card.dart';
import '../../models/tweet.dart';
import '../home/<USER>';

class ProfileScreen extends StatefulWidget {
  final int? userId;

  const ProfileScreen({super.key, this.userId});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Tweet> _userTweets = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserTweets();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadUserTweets() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final tweetsProvider = Provider.of<TweetsProvider>(
        context,
        listen: false,
      );

      final userId = widget.userId ?? authProvider.user?.id;
      if (userId != null) {
        final tweets = await tweetsProvider.getUserTweets(userId);
        setState(() {
          _userTweets = tweets;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to load tweets: $e')));
      }
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final user = authProvider.user;

          if (user == null) {
            return const Center(child: Text('User not found'));
          }

          return NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverAppBar(
                  expandedHeight: 200,
                  floating: false,
                  pinned: true,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Colors.blue.shade400, Colors.blue.shade600],
                        ),
                      ),
                      child: SafeArea(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            // Profile Image
                            CircleAvatar(
                              radius: 40,
                              backgroundColor: Colors.white,
                              backgroundImage:
                                  user.profileImageUrl != null
                                      ? NetworkImage(user.profileImageUrl!)
                                      : null,
                              child:
                                  user.profileImageUrl == null
                                      ? Text(
                                        user.displayName
                                            .substring(0, 1)
                                            .toUpperCase(),
                                        style: const TextStyle(
                                          fontSize: 32,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.blue,
                                        ),
                                      )
                                      : null,
                            ),
                            const SizedBox(height: 12),

                            // User Name
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  user.displayName,
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                if (user.verified) ...[
                                  const SizedBox(width: 8),
                                  const Icon(
                                    Icons.verified,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ],
                              ],
                            ),

                            // Username
                            Text(
                              '@${user.username}',
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.white70,
                              ),
                            ),
                            const SizedBox(height: 16),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Container(
                    color: Colors.white,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Bio
                        if (user.bio != null && user.bio!.isNotEmpty) ...[
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Text(
                              user.bio!,
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        ],

                        // Stats
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Row(
                            children: [
                              _buildStatItem('Following', user.followingCount),
                              const SizedBox(width: 20),
                              _buildStatItem('Followers', user.followersCount),
                              const SizedBox(width: 20),
                              _buildStatItem('Tweets', user.tweetsCount),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Tab Bar
                        TabBar(
                          controller: _tabController,
                          labelColor: Colors.blue,
                          unselectedLabelColor: Colors.grey,
                          indicatorColor: Colors.blue,
                          tabs: const [
                            Tab(text: 'Tweets'),
                            Tab(text: 'Replies'),
                            Tab(text: 'Media'),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ];
            },
            body: TabBarView(
              controller: _tabController,
              children: [
                // Tweets Tab
                _buildTweetsTab(),

                // Replies Tab
                const Center(
                  child: Text(
                    'Replies coming soon!',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ),

                // Media Tab
                const Center(
                  child: Text(
                    'Media coming soon!',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatItem(String label, int count) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          count.toString(),
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Text(label, style: const TextStyle(fontSize: 14, color: Colors.grey)),
      ],
    );
  }

  Widget _buildTweetsTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_userTweets.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No tweets yet',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _userTweets.length,
      itemBuilder: (context, index) {
        final tweet = _userTweets[index];
        return TweetCard(
          tweet: tweet,
          onLike: () {
            Provider.of<TweetsProvider>(
              context,
              listen: false,
            ).toggleLike(tweet.id);
            _loadUserTweets(); // Refresh to show updated counts
          },
          onRetweet: () {
            Provider.of<TweetsProvider>(
              context,
              listen: false,
            ).toggleRetweet(tweet.id);
            _loadUserTweets(); // Refresh to show updated counts
          },
          onReply: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (context) => TweetComposerScreen(replyToTweetId: tweet.id),
              ),
            ).then((result) {
              // Refresh tweets if a reply was posted
              if (result == true) {
                _loadUserTweets();
              }
            });
          },
        );
      },
    );
  }
}
